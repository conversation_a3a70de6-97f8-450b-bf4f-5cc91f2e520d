package config

import (
	"errors"
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port        int    `mapstructure:"PORT"`
	Environment string `mapstructure:"ENVIRONMENT"`

	// Database configuration
	Database DatabaseConfig `mapstructure:",squash"`

	// JWT configuration
	JWT JWTConfig `mapstructure:",squash"`

	// Okta configuration
	Okta OktaConfig `mapstructure:",squash"`

	// Slack configuration
	Slack SlackConfig `mapstructure:",squash"`

	// SCIM configuration
	SCIM SCIMConfig `mapstructure:",squash"`

	// CORS configuration
	CORS CORSConfig `mapstructure:",squash"`

	// Sentry configuration
	Sentry SentryConfig `mapstructure:",squash"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host     string `mapstructure:"DB_HOST"`
	Port     int    `mapstructure:"DB_PORT"`
	Name     string `mapstructure:"DB_NAME"`
	User     string `mapstructure:"DB_USER"`
	Password string `mapstructure:"DB_PASSWORD"`
	SSLMode  string `mapstructure:"DB_SSLMODE"`
}

// JWTConfig holds JWT configuration
type JWTConfig struct {
	SecretKey   string `mapstructure:"SECRET_KEY"`
	BackendURL  string `mapstructure:"BACKEND_URL"`
	FrontendURL string `mapstructure:"FRONTEND_URL"`
}

// OktaConfig holds Okta configuration
type OktaConfig struct {
	Domain       string `mapstructure:"OKTA_DOMAIN"`
	ClientID     string `mapstructure:"OKTA_CLIENT_ID"`
	ClientSecret string `mapstructure:"OKTA_CLIENT_SECRET"`
	RedirectURI  string `mapstructure:"OKTA_REDIRECT_URI"`
	AdminGroup   string `mapstructure:"IDP_ADMIN_GROUP"`
}

// SlackConfig holds Slack configuration
type SlackConfig struct {
	Token         string `mapstructure:"SLACK_TOKEN"`
	ChannelID     string `mapstructure:"SLACK_NOTIFICATION_CHANNEL_ID"`
	SigningSecret string `mapstructure:"SLACK_SIGNING_SECRET"`
}

// SCIMConfig holds SCIM configuration
type SCIMConfig struct {
	BearerToken string `mapstructure:"SCIM_BEARER_TOKEN"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowedOrigins   string `mapstructure:"CORS_ALLOWED_ORIGINS"`
	AllowCredentials bool   `mapstructure:"CORS_ALLOW_CREDENTIALS"`
	AllowedMethods   string `mapstructure:"CORS_ALLOWED_METHODS"`
	AllowedHeaders   string `mapstructure:"CORS_ALLOWED_HEADERS"`
}

// SentryConfig holds Sentry configuration
type SentryConfig struct {
	DSN              string  `mapstructure:"SENTRY_DSN"`
	Environment      string  `mapstructure:"SENTRY_ENVIRONMENT"`
	Release          string  `mapstructure:"SENTRY_RELEASE"`
	SampleRate       float64 `mapstructure:"SENTRY_SAMPLE_RATE"`
	TracesSampleRate float64 `mapstructure:"SENTRY_TRACES_SAMPLE_RATE"`
	Debug            bool    `mapstructure:"SENTRY_DEBUG"`
}

// Load loads configuration from environment variables and config files
func Load() (*Config, error) {
	// Set defaults first
	viper.SetDefault("PORT", 8080)
	viper.SetDefault("ENVIRONMENT", "development")
	viper.SetDefault("DB_HOST", "localhost")
	viper.SetDefault("DB_PORT", 5432)
	viper.SetDefault("DB_NAME", "stellar")
	viper.SetDefault("DB_USER", "stellar_user")
	viper.SetDefault("DB_SSLMODE", "disable")

	// CORS defaults
	viper.SetDefault("CORS_ALLOWED_ORIGINS", "http://localhost:3000")
	viper.SetDefault("CORS_ALLOW_CREDENTIALS", true)
	viper.SetDefault("CORS_ALLOWED_METHODS", "GET,POST,PUT,DELETE,OPTIONS")
	viper.SetDefault("CORS_ALLOWED_HEADERS", "Content-Type,Content-Length,Accept-Encoding,X-CSRF-Token,Authorization,accept,origin,Cache-Control,X-Requested-With")

	// Sentry defaults
	viper.SetDefault("SENTRY_SAMPLE_RATE", 1.0)
	viper.SetDefault("SENTRY_TRACES_SAMPLE_RATE", 0.1)
	viper.SetDefault("SENTRY_DEBUG", false)

	// Load .env file if it exists
	viper.SetConfigName(".env")
	viper.SetConfigType("env")
	viper.AddConfigPath(".")

	// Try to read .env file (don't fail if it doesn't exist)
	if err := viper.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if !errors.As(err, &configFileNotFoundError) {
			return nil, fmt.Errorf("error reading .env file: %w", err)
		}
	}

	// Also try to read YAML config file for backward compatibility
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Try to read YAML config file (don't fail if it doesn't exist)
	if err := viper.MergeInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError
		if !errors.As(err, &configFileNotFoundError) {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
	}

	// Enable reading from environment variables (highest priority)
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	// Validate required configuration
	if err := config.validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return &config, nil
}

// validate checks that all required configuration is present
func (c *Config) validate() error {
	if c.Database.Password == "" {
		return fmt.Errorf("DB_PASSWORD is required")
	}
	if c.JWT.SecretKey == "" {
		return fmt.Errorf("SECRET_KEY is required")
	}
	if c.JWT.BackendURL == "" {
		return fmt.Errorf("BACKEND_URL is required")
	}
	if c.JWT.FrontendURL == "" {
		return fmt.Errorf("FRONTEND_URL is required")
	}
	if c.Okta.Domain == "" {
		return fmt.Errorf("OKTA_DOMAIN is required")
	}
	if c.Okta.ClientID == "" {
		return fmt.Errorf("OKTA_CLIENT_ID is required")
	}
	if c.Okta.ClientSecret == "" {
		return fmt.Errorf("OKTA_CLIENT_SECRET is required")
	}
	if c.Okta.RedirectURI == "" {
		return fmt.Errorf("OKTA_REDIRECT_URI is required")
	}
	if c.SCIM.BearerToken == "" {
		return fmt.Errorf("SCIM_BEARER_TOKEN is required")
	}

	return nil
}

// GetDatabaseDSN returns the database connection string
func (c *Config) GetDatabaseDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Database.Host,
		c.Database.Port,
		c.Database.User,
		c.Database.Password,
		c.Database.Name,
		c.Database.SSLMode,
	)
}
