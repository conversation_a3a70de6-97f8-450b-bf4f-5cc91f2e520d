package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"stellar-go/internal/config"
	"stellar-go/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/slack-go/slack"
	"github.com/stretchr/testify/assert"
)

type fakeSlackAPI struct {
	openViewErr   error
	usersByID     map[string]*slack.User
}

func (f *fakeSlackAPI) OpenView(triggerID string, view slack.ModalViewRequest) (*slack.ViewResponse, error) {
	return &slack.ViewResponse{}, f.openViewErr
}
func (f *fakeSlackAPI) GetUserInfo(user string) (*slack.User, error) {
	u, ok := f.usersByID[user]
	if !ok {
		return &slack.User{ID: user}, nil
	}
	return u, nil
}

// Minimal in-memory UserService + OrbstarService using sqlite memory via gorm is overkill for unit test.
// We'll stub the required methods via embedding and overriding where needed.

type stubUserService struct{}

func (s *stubUserService) GetUserByEmail(email string) (*models.User, error) {
	if strings.HasPrefix(email, "missing") {
		return nil, assert.AnError
	}
	return &models.User{ID: 1, Email: email, FirstName: "F", LastName: "L"}, nil
}

type stubOrbstarService struct{}

func (s *stubOrbstarService) CreateOrbstars(giverID uint, req *models.CreateOrbstarRequest) ([]models.OrbstarResponse, error) {
	return []models.OrbstarResponse{{ID: 123}}, nil
}

func TestHandleOrbstarSlash_OpensModal(t *testing.T) {
	gin.SetMode(gin.TestMode)
	h := &SlackHandler{
		cfg:            &config.Config{},
		userService:    &stubUserService{},
		orbstarService: &stubOrbstarService{},
		slackAPI:       &fakeSlackAPI{},
	}

	form := url.Values{}
	form.Set("command", "/orbstar")
	form.Set("trigger_id", "TRIG")
	req := httptest.NewRequest(http.MethodPost, "/slack/commands/orbstar", strings.NewReader(form.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	w := httptest.NewRecorder()
	r := gin.New()
	r.POST("/slack/commands/orbstar", h.HandleOrbstarSlash)
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestHandleInteractivity_SubmitsModal_Succeeds(t *testing.T) {
	gin.SetMode(gin.TestMode)
	fake := &fakeSlackAPI{usersByID: map[string]*slack.User{
		"U_GIVER":   {ID: "U_GIVER", Profile: slack.UserProfile{Email: "<EMAIL>"}},
		"U_RECEIVE": {ID: "U_RECEIVE", Profile: slack.UserProfile{Email: "<EMAIL>"}},
	}}
	h := &SlackHandler{
		cfg:            &config.Config{},
		userService:    &stubUserService{},
		orbstarService: &stubOrbstarService{},
		slackAPI:       fake,
	}

	payload := slack.InteractionCallback{
		Type: slack.InteractionTypeViewSubmission,
		User: slack.User{ID: "U_GIVER"},
		View: slack.View{
			CallbackID: "orbstar_modal",
			State: &slack.ViewState{Values: map[string]map[string]slack.BlockAction{
				"recipient_block": {"recipient_select": {SelectedUser: "U_RECEIVE"}},
				"value_block":     {"value_select": {SelectedOption: slack.OptionBlockObject{Value: string(models.Innovation)}}},
				"message_block":   {"message_input": {Value: "Great job!"}},
			}},
		},
	}
	b, _ := json.Marshal(payload)
	form := url.Values{}
	form.Set("payload", string(b))
	req := httptest.NewRequest(http.MethodPost, "/slack/interactivity", strings.NewReader(form.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	w := httptest.NewRecorder()
	r := gin.New()
	r.POST("/slack/interactivity", h.HandleInteractivity)
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "\"response_action\":\"clear\"")
}

func TestHandleInteractivity_ValidationErrors(t *testing.T) {
	gin.SetMode(gin.TestMode)
	h := &SlackHandler{cfg: &config.Config{}, userService: &stubUserService{}, orbstarService: &stubOrbstarService{}, slackAPI: &fakeSlackAPI{}}
	payload := slack.InteractionCallback{Type: slack.InteractionTypeViewSubmission, View: slack.View{CallbackID: "orbstar_modal"}}
	b, _ := json.Marshal(payload)
	form := url.Values{}
	form.Set("payload", string(b))
	req := httptest.NewRequest(http.MethodPost, "/slack/interactivity", strings.NewReader(form.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	w := httptest.NewRecorder()
	r := gin.New()
	r.POST("/slack/interactivity", h.HandleInteractivity)
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "\"response_action\":\"errors\"")
}

