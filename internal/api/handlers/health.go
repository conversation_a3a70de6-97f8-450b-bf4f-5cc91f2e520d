package handlers

import (
	"errors"
	"net/http"

	"stellar-go/internal/models"
	"stellar-go/internal/sentry"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check endpoints
type HealthHandler struct{}

// NewHealthHandler creates a new health handler
func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// HealthCheck handles GET /
// @Summary Health check endpoint
// @Description Simple health check that returns the application name
// @Tags Health
// @Accept json
// @Produce plain
// @Success 200 {string} string "Stellar | Be lekker"
// @Router / [get]
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	c.String(http.StatusOK, "Stellar | Be lekker")
}

// FailEndpoint handles GET /fail
// @Summary Test error endpoint
// @Description Endpoint for testing error monitoring (Sentry)
// @Tags Health
// @Accept json
// @Produce json
// @Success 500 {object} models.ErrorResponse
// @Router /fail [get]
func (h *HealthHandler) FailEndpoint(c *gin.Context) {
	// Create a test error and report it to Sentry
	testErr := errors.New("this is a test error for monitoring")
	sentry.CaptureError(testErr)

	c.JSON(http.StatusInternalServerError, models.ErrorResponse{
		Error: "This is a test error for monitoring",
		Code:  "test_error",
	})
}
