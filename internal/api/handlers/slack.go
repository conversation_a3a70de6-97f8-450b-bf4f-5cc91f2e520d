package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"

	"stellar-go/internal/config"
	"stellar-go/internal/models"
	"stellar-go/internal/services"

	ginslack "github.com/slack-go/slack"

	"github.com/gin-gonic/gin"
)

type SlackAPI interface {
	OpenView(triggerID string, view ginslack.ModalViewRequest) (*ginslack.ViewResponse, error)
	GetUserInfo(user string) (*ginslack.User, error)
}

// SlackHandler contains dependencies for Slack endpoints
// It uses slack-go/slack for modal interactions and existing services for DB work
// and user lookup.
type SlackHandler struct {
	cfg            *config.Config
	userService    *services.UserService
	orbstarService *services.OrbstarService
	slackAPI       SlackAPI
}

func NewSlackHandler(cfg *config.Config, userService *services.UserService, orbstarService *services.OrbstarService) *SlackHandler {
	var api SlackAPI
	if cfg.Slack.Token != "" {
		api = ginslack.New(cfg.Slack.Token)
	}
	return &SlackHandler{cfg: cfg, userService: userService, orbstarService: orbstarService, slackAPI: api}
}

// HandleOrbstarSlash handles POST /slack/commands/orbstar
// It verifies the slash command and opens a modal for the user to fill in
func (h *SlackHandler) HandleOrbstarSlash(c *gin.Context) {
	// Parse slash command payload
	cmd, err := ginslack.SlashCommandParse(c.Request)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{Error: "Invalid slash command payload", Code: "invalid_payload"})
		return
	}

	if cmd.Command != "/orbstar" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{Error: "Unknown command", Code: "unknown_command"})
		return
	}

	if h.slackAPI == nil {
		c.JSON(http.StatusServiceUnavailable, models.ErrorResponse{Error: "Slack not configured", Code: "slack_unavailable"})
		return
	}

	// Build modal view with:
	// - users_select for recipient
	// - static_select for company value
	// - multiline text input for message
	userSelect := ginslack.NewOptionsSelectBlockElement(
		ginslack.OptTypeUser,
		ginslack.NewTextBlockObject(ginslack.PlainTextType, "Recipient", true, false),
		"recipient_select",
	)

	// Build options for values
	var options []*ginslack.OptionBlockObject
	for _, v := range models.GetAllCompanyValues() {
		opt := ginslack.NewOptionBlockObject(string(v),
			ginslack.NewTextBlockObject(ginslack.PlainTextType, string(v), true, false),
			nil,
		)
		options = append(options, opt)
	}
	valueSelect := ginslack.NewOptionsSelectBlockElement(
		ginslack.OptTypeStatic,
		ginslack.NewTextBlockObject(ginslack.PlainTextType, "Company value", true, false),
		"value_select",
		options...,
	)

	messageInput := ginslack.NewPlainTextInputBlockElement(
		ginslack.NewTextBlockObject(ginslack.PlainTextType, "Why are you giving this orbstar?", true, false),
		"message_input",
	).WithMultiline(true)

	// Assemble input blocks
	recipientBlock := ginslack.NewInputBlock(
		"recipient_block",
		ginslack.NewTextBlockObject(ginslack.PlainTextType, "Recipient", true, false),
		nil,
		userSelect,
	)
	valueBlock := ginslack.NewInputBlock(
		"value_block",
		ginslack.NewTextBlockObject(ginslack.PlainTextType, "Company value", true, false),
		nil,
		valueSelect,
	)
	messageBlock := ginslack.NewInputBlock(
		"message_block",
		ginslack.NewTextBlockObject(ginslack.PlainTextType, "Message", true, false),
		nil,
		messageInput,
	)

	modal := ginslack.ModalViewRequest{
		Type:   ginslack.VTModal,
		Title:  ginslack.NewTextBlockObject(ginslack.PlainTextType, "Give an Orbstar", true, false),
		Close:  ginslack.NewTextBlockObject(ginslack.PlainTextType, "Cancel", true, false),
		Submit: ginslack.NewTextBlockObject(ginslack.PlainTextType, "Send", true, false),
		Blocks: ginslack.Blocks{BlockSet: []ginslack.Block{recipientBlock, valueBlock, messageBlock}},
		// CallbackID lets us identify this modal on submission
		CallbackID: "orbstar_modal",
	}

	// Open modal using trigger ID
	_, err = h.slackAPI.OpenView(cmd.TriggerID, modal)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{Error: fmt.Sprintf("Failed to open modal: %v", err), Code: "open_view_failed"})
		return
	}

	// Respond with 200 OK and empty body to acknowledge the slash command
	c.Status(http.StatusOK)
}

// HandleInteractivity handles POST /slack/interactivity for modal submissions
func (h *SlackHandler) HandleInteractivity(c *gin.Context) {
	payload := c.PostForm("payload")
	if payload == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{Error: "Missing payload", Code: "missing_payload"})
		return
	}
	var cb ginslack.InteractionCallback
	if err := json.Unmarshal([]byte(payload), &cb); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{Error: "Invalid payload", Code: "invalid_payload"})
		return
	}

	// Only handle view submissions from our modal
	if cb.Type != ginslack.InteractionTypeViewSubmission || cb.View.CallbackID != "orbstar_modal" {
		c.Status(http.StatusOK)
		return
	}

	// Extract values from the modal state
	// State.Values[blockID][actionID]
	state := cb.View.State
	if state == nil {
		respondViewErrors(c, map[string]string{"message_block": "Missing form state"})
		return
	}

	recipient := state.Values["recipient_block"]["recipient_select"].SelectedUser
	valueOpt := state.Values["value_block"]["value_select"].SelectedOption
	message := state.Values["message_block"]["message_input"].Value

	if recipient == "" || valueOpt.Value == "" || message == "" {
		errs := map[string]string{}
		if recipient == "" {
			errs["recipient_block"] = "Please choose a recipient"
		}
		if valueOpt.Value == "" {
			errs["value_block"] = "Please choose a company value"
		}
		if message == "" {
			errs["message_block"] = "Please add a message"
		}
		respondViewErrors(c, errs)
		return
	}

	// Look up giver and receiver emails via Slack API
	if h.slackAPI == nil {
		respondViewErrors(c, map[string]string{"recipient_block": "Slack not configured"})
		return
	}

	// Get receiver Slack profile to obtain email
	receiverInfo, err := h.slackAPI.GetUserInfo(recipient)
	if err != nil || receiverInfo.Profile.Email == "" {
		respondViewErrors(c, map[string]string{"recipient_block": "Could not resolve recipient email"})
		return
	}

	// Get giver Slack profile (the user who opened the modal)
	giverSlackID := cb.User.ID
	giverInfo, err := h.slackAPI.GetUserInfo(giverSlackID)
	if err != nil || giverInfo.Profile.Email == "" {
		respondViewErrors(c, map[string]string{"recipient_block": "Could not resolve your email in Slack"})
		return
	}

	// Map emails to internal users
	giver, err := h.userService.GetUserByEmail(giverInfo.Profile.Email)
	if err != nil {
		respondViewErrors(c, map[string]string{"recipient_block": "Your user is not registered in Stellar"})
		return
	}
	receiver, err := h.userService.GetUserByEmail(receiverInfo.Profile.Email)
	if err != nil {
		respondViewErrors(c, map[string]string{"recipient_block": "Recipient is not registered in Stellar"})
		return
	}

	// Validate company value
	cv := models.CompanyValue(valueOpt.Value)
	if !cv.IsValid() {
		respondViewErrors(c, map[string]string{"value_block": "Invalid company value"})
		return
	}

	// Create orbstar via service (single recipient)
	req := models.CreateOrbstarRequest{
		Receiver:    float64(receiver.ID), // service expects numbers as float64 in Receiver interface
		Description: message,
		Value:       cv,
	}
	resps, err := h.orbstarService.CreateOrbstars(giver.ID, &req)
	if err != nil || len(resps) == 0 {
		respondViewErrors(c, map[string]string{"message_block": "Failed to create orbstar"})
		return
	}

	// Success: clear the modal
	c.JSON(http.StatusOK, ginslack.NewClearViewSubmissionResponse())
}

func respondViewErrors(c *gin.Context, errs map[string]string) {
	c.JSON(http.StatusOK, ginslack.NewErrorsViewSubmissionResponse(errs))
}

