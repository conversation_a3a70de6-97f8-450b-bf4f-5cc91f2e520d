package handlers

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"stellar-go/internal/auth"
	"stellar-go/internal/config"
	"stellar-go/internal/models"
	"stellar-go/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	jwtManager   *auth.JWTManager
	oktaManager  *auth.OktaManager
	userService  *services.UserService
	tokenService *services.TokenService
	config       *config.Config
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(jwtManager *auth.JWTManager, oktaManager *auth.OktaManager, userService *services.UserService, tokenService *services.TokenService, cfg *config.Config) *AuthHandler {
	return &AuthHandler{
		jwtManager:   jwtManager,
		oktaManager:  oktaManager,
		userService:  userService,
		tokenService: tokenService,
		config:       cfg,
	}
}

// <PERSON><PERSON> handles GET /login
// @Summary Initiate Okta login
// @Description Redirects to Okta for authentication
// @Tags Authentication
// @Accept json
// @Produce json
// @Param return_to query string false "URL to redirect to after successful authentication"
// @Success 302 {string} string "Redirect to Okta login"
// @Router /login [get]
func (h *AuthHandler) Login(c *gin.Context) {
	// Extract and validate return_to parameter
	returnTo := c.Query("return_to")
	if returnTo != "" && !h.isValidReturnURL(returnTo) {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid return URL",
			Code:  "invalid_return_url",
		})
		return
	}

	authURL, state, _, err := h.oktaManager.GenerateAuthURL()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to generate authorization URL",
			Code:  "auth_url_error",
		})
		return
	}

	// Store return_to parameter in session if provided
	if returnTo != "" {
		h.oktaManager.StoreReturnURL(state, returnTo)
	}

	// Prevent caching of the redirect
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	c.Redirect(http.StatusFound, authURL)
}

// HandleIDPInitiatedLogin handles login initiated from Okta (IDP-initiated)
// @Summary Handle IDP-initiated login
// @Description Handles login initiated from Okta's dashboard while preserving return URL
// @Tags Authentication
// @Accept json
// @Produce json
// @Param return_to query string false "URL to redirect to after successful authentication"
// @Success 302 {string} string "Redirect to standard login flow"
// @Router /idp-login [get]
func (h *AuthHandler) HandleIDPInitiatedLogin(c *gin.Context) {
	// Extract return URL from query params
	returnTo := c.Query("return_to")
	if returnTo == "" {
		returnTo = "/dashboard" // default redirect after login
	}

	// Validate return URL to prevent open redirects
	if !h.isValidReturnURL(returnTo) {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid return URL",
			Code:  "invalid_return_url",
		})
		return
	}

	// Store return URL in a temporary session for post-auth redirect
	// We'll use a simple approach by storing it in the URL state parameter
	// This is secure because the state parameter is validated during the OAuth flow

	// For now, we'll redirect to the standard login flow
	// The return_to parameter will be handled in the callback
	loginURL := fmt.Sprintf("/login?return_to=%s", url.QueryEscape(returnTo))

	// Prevent caching of the redirect
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	c.Redirect(http.StatusFound, loginURL)
}

// LoginCallback handles GET /login/callback
// @Summary Handle Okta callback
// @Description Processes the authorization code from Okta and returns JWT tokens
// @Tags Authentication
// @Accept json
// @Produce json
// @Param code query string true "Authorization code from Okta"
// @Param state query string false "State parameter for CSRF protection"
// @Success 302 {string} string "Redirect to frontend with tokens"
// @Failure 403 {object} models.ErrorResponse
// @Router /login/callback [get]
func (h *AuthHandler) LoginCallback(c *gin.Context) {
	code := c.Query("code")
	state := c.Query("state")

	if code == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Authorization code is required",
			Code:  "missing_code",
		})
		return
	}

	// Exchange code for tokens
	tokenResponse, err := h.oktaManager.ExchangeCodeForTokens(code, state)
	if err != nil {
		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error: "Failed to exchange authorization code",
			Code:  "token_exchange_error",
		})
		return
	}

	// Get user info from Okta
	userInfo, err := h.oktaManager.GetUserInfo(tokenResponse.AccessToken)
	if err != nil {
		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error: "Failed to get user information",
			Code:  "user_info_error",
		})
		return
	}

	// Create or update user in database
	user, err := h.userService.CreateOrUpdateUserFromOkta(
		userInfo.Email,
		userInfo.FirstName,
		userInfo.LastName,
		userInfo.IsAdmin,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to create/update user",
			Code:  "user_creation_error",
		})
		return
	}

	// Generate internal JWT tokens
	accessToken, refreshToken, err := h.jwtManager.GenerateTokenPair(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to generate tokens",
			Code:  "token_generation_error",
		})
		return
	}

	// Store JWT tokens in temporary session
	sessionID, err := h.oktaManager.StoreTokenSession(accessToken, refreshToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to create session",
			Code:  "session_creation_error",
		})
		return
	}

	// Get return URL from stored session if available
	returnTo := h.oktaManager.GetReturnURL(state)
	if returnTo == "" {
		returnTo = "/dashboard" // default fallback
	}

	// Redirect to frontend with session ID and return URL
	frontendURL := h.config.JWT.FrontendURL
	redirectURL := fmt.Sprintf("%s?session=%s&return_to=%s",
		frontendURL,
		url.QueryEscape(sessionID),
		url.QueryEscape(returnTo))

	// Prevent caching of the redirect
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	c.Redirect(http.StatusFound, redirectURL)
}

// ExchangeSession handles POST /auth/exchange
// @Summary Exchange session for JWT tokens
// @Description Exchanges a temporary session ID for JWT access and refresh tokens
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.SessionExchangeRequest true "Session exchange request"
// @Success 200 {object} models.SessionExchangeResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /auth/exchange [post]
func (h *AuthHandler) ExchangeSession(c *gin.Context) {
	var req models.SessionExchangeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid request body",
			Code:  "invalid_request",
		})
		return
	}

	if req.SessionID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Session ID is required",
			Code:  "missing_session_id",
		})
		return
	}

	// Exchange session for tokens
	accessToken, refreshToken, err := h.oktaManager.ExchangeTokenSession(req.SessionID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error: "Invalid or expired session",
			Code:  "invalid_session",
		})
		return
	}

	// Return tokens in JSON response
	c.JSON(http.StatusOK, models.SessionExchangeResponse{
		Access:  accessToken,
		Refresh: refreshToken,
	})
}

// isValidReturnURL validates that the return URL is safe to redirect to
func (h *AuthHandler) isValidReturnURL(returnURL string) bool {
	// Allow relative URLs that start with /
	if strings.HasPrefix(returnURL, "/") && !strings.HasPrefix(returnURL, "//") {
		return true
	}

	// Parse the URL to validate it
	parsedURL, err := url.Parse(returnURL)
	if err != nil {
		return false
	}

	// Only allow URLs that match the frontend domain
	frontendURL, err := url.Parse(h.config.JWT.FrontendURL)
	if err != nil {
		return false
	}

	// Allow same origin URLs
	return parsedURL.Scheme == frontendURL.Scheme &&
		parsedURL.Host == frontendURL.Host
}

// RefreshToken handles POST /auth/refresh
// @Summary Refresh JWT tokens
// @Description Exchange refresh token for new access and refresh tokens
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body models.RefreshTokenRequest true "Refresh token request"
// @Success 200 {object} models.TokenResponse
// @Failure 400 {object} models.ErrorResponse
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid request body",
			Code:  "invalid_request",
		})
		return
	}

	// Refresh tokens with blacklist check
	newAccessToken, newRefreshToken, err := h.jwtManager.RefreshTokensWithBlacklistCheck(req.Access, req.Refresh, h.tokenService)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid refresh token",
			Code:  "invalid_refresh_token",
		})
		return
	}

	response := models.TokenResponse{
		Access:  newAccessToken,
		Refresh: newRefreshToken,
	}

	c.JSON(http.StatusOK, response)
}

// Logout handles POST /auth/logout
// @Summary Logout user
// @Description Invalidates the user's refresh token and logs them out
// @Tags Authentication
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.LogoutRequest true "Logout request"
// @Success 200 {object} models.SuccessResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	var req models.LogoutRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid request body",
			Code:  "invalid_request",
		})
		return
	}

	if req.Refresh == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Refresh token is required",
			Code:  "missing_refresh_token",
		})
		return
	}

	// Extract token ID from refresh token
	tokenID, err := h.jwtManager.ExtractTokenID(req.Refresh)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid refresh token format",
			Code:  "invalid_token_format",
		})
		return
	}

	// Validate refresh token to get user ID and expiration
	claims, err := h.jwtManager.ValidateRefreshToken(req.Refresh)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid refresh token",
			Code:  "invalid_refresh_token",
		})
		return
	}

	// Blacklist the refresh token
	err = h.tokenService.BlacklistRefreshToken(tokenID, claims.UserID, claims.ExpiresAt.Time)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to logout",
			Code:  "logout_error",
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Status: "Logged out successfully",
	})
}
