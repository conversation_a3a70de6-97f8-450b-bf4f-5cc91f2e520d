package auth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SlackVerificationMiddleware verifies incoming Slack requests using the signing secret.
// It validates the Slack-Signature and Slack-Request-Timestamp headers to prevent tampering
// and replay attacks. Rejects requests older than 5 minutes or with invalid signatures.
func SlackVerificationMiddleware(signingSecret string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if signingSecret == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Slack signing not configured", "code": "slack_not_configured"})
			return
		}

		// Extract headers
		timestamp := c.Request.Header.Get("X-Slack-Request-Timestamp")
		sigHeader := c.Request.Header.Get("X-Slack-Signature")

		if timestamp == "" || sigHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing Slack signature headers", "code": "missing_signature"})
			return
		}

		// Verify timestamp is within 5 minutes to prevent replay attacks
		ts, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid timestamp", "code": "invalid_timestamp"})
			return
		}
		if abs(time.Now().Unix()-ts) > 60*5 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Expired timestamp", "code": "expired_timestamp"})
			return
		}

		// Read the raw body (Slack signs the raw payload). Gin may have already read it into c.Request.Body.
		// We'll read and then replace the body so downstream handlers can read it again.
		var bodyBytes []byte
		if c.Request.Body != nil {
			bodyBytes, err = io.ReadAll(c.Request.Body)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unable to read body", "code": "read_body_failed"})
				return
			}
		}
		c.Request.Body = io.NopCloser(strings.NewReader(string(bodyBytes)))

		// Build the basestring: v0:timestamp:body
		base := strings.Join([]string{"v0", timestamp, string(bodyBytes)}, ":")

		mac := hmac.New(sha256.New, []byte(signingSecret))
		mac.Write([]byte(base))
		computed := "v0=" + hex.EncodeToString(mac.Sum(nil))

		// Compare signatures in constant-time
		if !hmac.Equal([]byte(computed), []byte(sigHeader)) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Slack signature", "code": "invalid_signature"})
			return
		}

		// Restore body for form parsing in handlers
		c.Request.Body = io.NopCloser(strings.NewReader(string(bodyBytes)))

		c.Next()
	}
}

func abs(n int64) int64 {
	if n < 0 {
		return -n
	}
	return n
}

