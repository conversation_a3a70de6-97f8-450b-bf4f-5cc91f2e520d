package auth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func signBody(secret, ts, body string) string {
	base := strings.Join([]string{"v0", ts, body}, ":")
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(base))
	return "v0=" + hex.EncodeToString(h.Sum(nil))
}

func TestSlackVerificationMiddleware_Valid(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	secret := "test_secret"
	r.POST("/test", SlackVerificationMiddleware(secret), func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	ts := time.Now().Unix()
	body := "payload=test"
	req := httptest.NewRequest(http.MethodPost, "/test", strings.NewReader(body))
	req.Header.Set("X-Slack-Request-Timestamp", strconv.FormatInt(ts, 10))
	req.Header.Set("X-Slack-Signature", signBody(secret, strconv.FormatInt(ts, 10), body))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestSlackVerificationMiddleware_InvalidSignature(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	secret := "test_secret"
	r.POST("/test", SlackVerificationMiddleware(secret), func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	req := httptest.NewRequest(http.MethodPost, "/test", strings.NewReader("x=1"))
	req.Header.Set("X-Slack-Request-Timestamp", "1234567890")
	req.Header.Set("X-Slack-Signature", "v0=deadbeef")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestSlackVerificationMiddleware_Expired(t *testing.T) {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	secret := "test_secret"
	r.POST("/test", SlackVerificationMiddleware(secret), func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})

	old := time.Now().Add(-6 * time.Minute).Unix()
	body := "test=1"
	req := httptest.NewRequest(http.MethodPost, "/test", strings.NewReader(body))
	req.Header.Set("X-Slack-Request-Timestamp", strconv.FormatInt(old, 10))
	req.Header.Set("X-Slack-Signature", signBody(secret, strconv.FormatInt(old, 10), body))
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

